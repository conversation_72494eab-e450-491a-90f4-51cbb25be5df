version: 3

includes:
  common:
    taskfile: /taskfiles # This is common taskfile that is included in docker image
    optional: true

env:
  SHA: '{{ .SHA }}' # Commit hash that triggered pipeline
  WORKSPACE: '{{ .WORKSPACE }}' # It will be injected by CI
  REPO_REVISION: '{{ .REPO_REVISION }}' # Branch or tag that triggered pipeline
  APP_NAME: 'cabcharge-gateway-web'
  HELM_CHART: *****************:cdgtaxi/cabcharge-k8s.git
  VALUE_FILE: cabcharge-gateway-web-values.yaml
  AWS_DEFAULT_REGION: 'ap-southeast-1'
  IMAGE_REGISTRY: '211125325566.dkr.ecr.{{ .AWS_DEFAULT_REGION }}.amazonaws.com'
  IMAGE_NAME: '{{ .IMAGE_REGISTRY }}/{{ .APP_NAME }}'
  IMAGE_NAME_FULL: '{{ .IMAGE_NAME }}:{{ substr 0 8 .SHA }}'
  DOCKERFILE: 'Dockerfile'
  SONAR_SCANNER_VERSION: 5.0.1.3006
  SONAR_HOST: https://sonarcloud.io
  SONAR_PROJECT:  cdgtaxi_cabcharge-gateway-web
  SONAR_ORGANIZATION: cdg-zig
  SONAR_BRANCH: '{{ .REPO_REVISION }}'
dotenv:
  - .env

vars:
  ENVIRONMENT:
    sh: echo $([[ {{ .REPO_REVISION }} == "sit" ]] && echo "sit" || ([[ {{ .REPO_REVISION }} == "uat" || {{ .REPO_REVISION }} == "uat1" ]] && echo "uat1") || ([[ {{ .REPO_REVISION }} == "master" ]] && echo "prod") || echo "sit")
  secrets-manager:
    map:
      {
        SONAR_TOKEN: 'arn:aws:secretsmanager:ap-southeast-1:350155334257:secret:ngp-terraform/sonarcloud-8iGTfy:SONAR_TOKEN'
      }

tasks:
  init:
    cmds:
      - touch .env
      - echo "Start integrating secret"
      - for: {var: secrets-manager}
        cmd: task common:aws:parse-secrets-manager SECRET_KEY={{.KEY}} SECRET_SPEC={{.ITEM}}
      - for: {var: parameter-store}
        cmd: task common:aws:parse-paramater-store PARAM_KEY={{.KEY}} PARAM_SPEC={{.ITEM}}
  install:
    cmds:
      - echo "Install"
    internal: true
  pre_build:
    cmds:
      - task: install
      - echo Uploading results to SonarCloud...
      - sonar-scanner -Dsonar.organization=$SONAR_ORGANIZATION -Dsonar.projectKey=$SONAR_PROJECT -Dsonar.host.url=$SONAR_HOST -Dsonar.token=$SONAR_TOKEN -Dsonar.branch.name=$SONAR_BRANCH -Dproject.settings=sonar-project.properties
    internal: true
  post_build:
    cmds:
      - echo -n {{ .IMAGE_NAME_FULL }} > {{ .IMAGE_RESULT }}
      - echo -n {{ .HELM_CHART }} > {{ .HELM_CHART_RESULT }}
      - echo -n {{ .VALUE_FILE }} > {{ .VALUE_FILE_RESULT }}
    internal: true
  build:
    cmds:
      - task: pre_build
      - defer:
          task: post_build
      - task: sonar
      - cmd: aws ecr get-login-password --region ${AWS_DEFAULT_REGION} | buildah login --username AWS --password-stdin ${IMAGE_REGISTRY}
      # Dev to edit from here
      - task: 'common:buildah'
        vars:
          TAG: '{{ .IMAGE_NAME_FULL }}'
          DOCKERFILE: '{{ .DOCKERFILE }}'
          OTHER_OPTS: >-
            --build-arg ECR_URI=$IMAGE_REGISTRY
            --build-arg ENV={{ .ENVIRONMENT }}
            
  sonar:
    cmds:
      - npm install
      - npm run test
      - echo Uploading results to SonarCloud...
      - echo $SONAR_SCANNER_JAVA_OPTS
      - >
        sonar-scanner
        -Dsonar.organization=$SONAR_ORGANIZATION
        -Dsonar.projectKey=$SONAR_PROJECT
        -Dsonar.host.url=$SONAR_HOST
        -Dsonar.token=$SONAR_TOKEN
        -Dsonar.branch.name=$SONAR_BRANCH
        -Dproject.settings=sonar-project.properties