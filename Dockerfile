# Stage 1: Build frontend
ARG ECR_URI
FROM ${ECR_URI}/node:16.15.0-alpine AS build
 
WORKDIR /usr/src/app

COPY package*.json ./

RUN npm install 

COPY . .

# Build the React app for production
ARG ENV
RUN npm run build:${ENV}
 
# Stage 2: Serve it using Ngnix
FROM ${ECR_URI}/nginx:1.27.0-alpine
RUN rm -rf /usr/share/nginx/html/index.html
COPY --from=build /usr/src/app/build /usr/share/nginx/html 
COPY ./nginx.conf /etc/nginx/nginx.conf 
EXPOSE 80
# ENTRYPOINT ["nginx","-g","daemon off;"] 
