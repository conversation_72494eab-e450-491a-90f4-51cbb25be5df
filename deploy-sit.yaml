apiVersion: apps/v1
kind: Deployment
metadata:
  name: SERVICE
spec:
  replicas: 1
  selector:
    matchLabels:
      app: SERVICE
  template:
    metadata:
      labels:
        app: SERVICE
    spec:
      containers:
      - name: SERVICE-c
        image: IMAGE
        ports:
        - containerPort: 80
        imagePullPolicy: Always
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
        livenessProbe:
          httpGet:
            path: /
            port: 80
            scheme: HTTP
          initialDelaySeconds: 20
          periodSeconds: 10
          successThreshold: 1
        readinessProbe:
          httpGet:
            path: /
            port: 80
            scheme: HTTP
          initialDelaySeconds: 20
          periodSeconds: 10
          successThreshold: 1
        lifecycle:
          preStop:
            exec:
              command:
              - sh
              - -c 
              - "sleep 5"
      imagePullSecrets:
      - name: swr-secret
---
apiVersion: v1
kind: Service
metadata:
  name: SERVICE
  labels:
    app: SERVICE
#   annotations:
#     kubernetes.io/elb.class: performance
#     kubernetes.io/elb.health-check-flag: 'on'
#     kubernetes.io/elb.health-check-option: '{"protocol":"TCP","delay":"5","timeout":"10","max_retries":"3"}'
#     kubernetes.io/elb.id: bae4935a-5807-4fe0-873b-a8cbb3a14b05
#     kubernetes.io/elb.lb-algorithm: ROUND_ROBIN
#     kubernetes.io/elb.mark: '0'
spec:
  selector:
    app: SERVICE
  ports:
    - name: http
      port: 38116
      targetPort: 80
  type: ClusterIP
  sessionAffinity: None
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: SERVICE
spec:
  ingressClassName: kong-sit-external
  rules:
    - host: portalweb.zigpro.apps.nonprod-cce-1-27.cce.internal.sit.sg.huawei.zig.systems
      http:
        paths:
          - path: /
            pathType: ImplementationSpecific
            backend:
              service:
                name: SERVICE
                port:
                  name: http
    - host: portalweb.zigpro.apps.internal.sit.sg.huawei.zig.systems
      http:
        paths:
          - path: /
            pathType: ImplementationSpecific
            backend:
              service:
                name: SERVICE
                port:
                  name: http
    - host: portalweb.zigpro.nprod.zig.systems
      http:
        paths:
          - path: /
            pathType: ImplementationSpecific
            backend:
              service:
                name: SERVICE
                port:
                  name: http
    - host: cabchargegatewayweb-sit.apps.sit.sg.huawei.zig.systems
      http:
        paths:
          - path: /
            pathType: ImplementationSpecific
            backend:
              service:
                name: SERVICE
                port:
                  name: http
    - host: cabchargegatewayweb-sit.zig.live
      http:
        paths:
          - path: /
            pathType: ImplementationSpecific
            backend:
              service:
                name: SERVICE
                port:
                  name: http                  