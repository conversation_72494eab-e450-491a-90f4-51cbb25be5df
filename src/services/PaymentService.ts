import axios from 'axios';
import { CardAuthorizeRequest, CardAuthorizeResponse } from '../models/CardAuthorize';
import config from '../config/Config';
import { TransactionDetails } from '../models/TransactionDetails';
import { InitTrasactionSessionRequest, InitTrasactionSessionResponse } from '../models/InitTransactionSession';

const env = process.env.REACT_APP_ENV || "dev";
const baseURL = config[env]?.baseURL;

console.log('Current environment:', env);
console.log('Current baseURL:', baseURL);

export const submitPayment = async (
  transactionId: string,
  cardHolderName: string,
  cardNumber: string,
  expiryDate: string
): Promise<CardAuthorizeResponse> => {
  const request: CardAuthorizeRequest = {
    transactionId,
    cardHolderName,
    cardNumber,
    expiryDate
  };

  try {
    const response = await axios.post<CardAuthorizeResponse>(`${baseURL}/v1.0/transactions/check-card`, request);
    return response.data;
  } catch (error) {
    throw new Error('Failed to submit payment.');
  }
};

export const fetchTransactionDetails = async (transactionID: string) => {
  try {
    const response = await axios.get<TransactionDetails>(`${baseURL}/v1.0/transactions/${transactionID}`);
    return response.data;
  } catch (error) {
    console.error('Error retrieving transaction details:', error);
    throw error;
  }
};

export const initTransactionSession = async (request: InitTrasactionSessionRequest) => {
  try {
    const response = await axios.post<InitTrasactionSessionResponse>(`${baseURL}/v1.0/transactions/init-session`, request);
    return response.data;
  } catch (error) {
    console.error('Error retrieving transaction details:', error);
    throw error;
  }
};
