export const createMonths = () : string[] =>{
    const monthNames = [
        'January', 'February', 'March', 'April', 'May', 'June', 'July',
        'August', 'September', 'October', 'November', 'December'
      ];
      return monthNames;
}

export const createAvailableYears = () : number[] => {
    // Generate available years
    const currentYear = new Date().getFullYear();
    const next10Years = Array.from({ length: 11 }, (_, index) => currentYear + index);
    return next10Years;
}
