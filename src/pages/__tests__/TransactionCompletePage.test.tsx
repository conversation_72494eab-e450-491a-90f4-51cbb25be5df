import { render, screen, queryByAttribute, fireEvent, act } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import axios from 'axios';
import userEvent from '@testing-library/user-event';
import TransactionCompletePage from '../TransactionCompletePage';

// Mock jest and set the type
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('Transaction complete page test', () => {

    test('loading default page', () => {
        const view = render(
            <MemoryRouter initialEntries={['?transactionId=12345']}>
                <TransactionCompletePage />
            </MemoryRouter>)
        expect(view).not.toBeNull()
    })

    test('load transaction detail', async () => {
        mockedAxios.get.mockResolvedValueOnce({
            data: {
                returnURL: 'string',
                requestId: 'string',
                transactionId: 'string',
                transactionDateTime: 'string',
                responseCode: '00',
                responseMessage: 'string',
                cardToken: 'string',
                expiryDate: 'string',
                customData: 'string',
                signature: 'string'
            }
        });

        const view = render(
            <MemoryRouter initialEntries={['?transactionId=12345']}>
                <TransactionCompletePage />
            </MemoryRouter>)

        const formElement = await screen.findByRole('form')
        console.log('formElement', formElement)
        expect(view).not.toBeNull()
    })

})
