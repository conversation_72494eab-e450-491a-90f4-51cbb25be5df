import { render, screen, queryByAttribute, fireEvent, waitFor } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import CheckoutPage from '../CheckoutPage';
import { initTransactionSession, submitPayment } from '../../services/PaymentService';

// Mock the PaymentService module
jest.mock('../../services/PaymentService', () => ({
    initTransactionSession: jest.fn(),
    submitPayment: jest.fn(),
}));

const mockInitTransactionSession = initTransactionSession as jest.MockedFunction<typeof initTransactionSession>;
const mockSubmitPayment = submitPayment as jest.MockedFunction<typeof submitPayment>;

describe('Checkout page test', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    test('loading default page', () => {
        const view = render(
            <MemoryRouter>
                <CheckoutPage />
            </MemoryRouter>)
        expect(view).not.toBeNull()
    })

    test('loading page with params - successful initialization', async () => {
        // Mock successful API response
        mockInitTransactionSession.mockResolvedValueOnce({
            responseCode: "00",
            responseMessage: "Success",
            transactionID: "12345",
            orderNumber: "67890",
            cancelURL: "http://example.com/cancel",
            termURL: "",
            MD: "",
            twoFaWebURL: "",
            paReq: ""
        });

        const getById = queryByAttribute.bind(null, 'id');
        const view = render(
            <MemoryRouter initialEntries={['?transactionId=12345&paReq=67890']}>
                <CheckoutPage />
            </MemoryRouter>
        );

        await waitFor(() => screen.findByText('Card Holder Name'));

        const twoFAform = getById(view.container, 'twoFAForm');
        expect(twoFAform).toBeInTheDocument();
        expect(view).not.toBeNull();
    })

    test('loading page with params - invalid card name', async () => {
        // Mock successful initialization
        mockInitTransactionSession.mockResolvedValueOnce({
            responseCode: "00",
            responseMessage: "Success",
            transactionID: "12345",
            orderNumber: "67890",
            cancelURL: "http://example.com/cancel",
            termURL: "",
            MD: "",
            twoFaWebURL: "",
            paReq: ""
        });

        // Mock submit payment failure for validation
        mockSubmitPayment.mockRejectedValueOnce(new Error('Failed to submit payment.'));

        const getById = queryByAttribute.bind(null, 'id');
        const view = render(
            <MemoryRouter initialEntries={['?transactionId=12345&paReq=67890']}>
                <CheckoutPage />
            </MemoryRouter>
        );

        await waitFor(() => screen.findByText('Card Holder Name'));

        const cardHolderNameElement = screen.getByLabelText('cardHolderName');
        const cardNumberElement = screen.getByLabelText('cardNumber');

        fireEvent.change(cardHolderNameElement, { target: { value: 'A' } });
        fireEvent.change(cardNumberElement, { target: { value: '1234567890' } });

        fireEvent.click(screen.getByText('Submit'));

        const twoFAform = getById(view.container, 'twoFAForm');
        expect(twoFAform).toBeInTheDocument();
        expect(view).not.toBeNull();
    })
 
    test('loading page with params - invalid card number', async () => {
        // Mock successful initialization
        mockInitTransactionSession.mockResolvedValueOnce({
            responseCode: "00",
            responseMessage: "Success",
            transactionID: "12345",
            orderNumber: "123456",
            cancelURL: "http://example.com/cancel",
            termURL: "",
            MD: "",
            twoFaWebURL: "",
            paReq: ""
        });

        // Mock submit payment failure for validation
        mockSubmitPayment.mockRejectedValueOnce(new Error('Failed to submit payment.'));

        const getById = queryByAttribute.bind(null, 'id');
        const view = render(
            <MemoryRouter initialEntries={['?transactionId=12345&paReq=67890']}>
                <CheckoutPage />
            </MemoryRouter>
        );

        await waitFor(() => screen.findByText('Card Holder Name'));

        const cardHolderNameElement = screen.getByLabelText('cardHolderName');
        const cardNumberElement = screen.getByLabelText('cardNumber');

        fireEvent.change(cardHolderNameElement, { target: { value: 'Card holder name' } });
        fireEvent.change(cardNumberElement, { target: { value: '1234567890' } });

        fireEvent.click(screen.getByText('Submit'));

        fireEvent.click(screen.getByText('Cancel'));

        const twoFAform = getById(view.container, 'twoFAForm');
        expect(twoFAform).toBeInTheDocument();
        expect(view).not.toBeNull();
    })
 
    test('loading page with params - input card and submit', async () => {
        // Mock successful initialization
        mockInitTransactionSession.mockResolvedValueOnce({
            responseCode: "00",
            responseMessage: "Success",
            transactionID: "12345",
            orderNumber: "67890",
            cancelURL: "http://example.com/cancel",
            termURL: "",
            MD: "",
            twoFaWebURL: "",
            paReq: ""
        });

        // Mock successful payment submission
        mockSubmitPayment.mockResolvedValueOnce({
            transactionID: "12345",
            responseCode: "00",
            responseMessage: "Success",
            termURL: "http://example.com/term",
            MD: "test-md",
            twoFaWebURL: "http://example.com/2fa",
            paReq: "test-pareq"
        });

        const getById = queryByAttribute.bind(null, 'id');
        const view = render(
            <MemoryRouter initialEntries={['?transactionId=12345&paReq=67890']}>
                <CheckoutPage />
            </MemoryRouter>
        );

        await waitFor(() => screen.findByText('Card Holder Name'));

        const cardHolderNameElement = screen.getByLabelText('cardHolderName');
        const cardNumberElement = screen.getByLabelText('cardNumber');

        fireEvent.change(cardHolderNameElement, { target: { value: 'Card holder' } });
        fireEvent.change(cardNumberElement, { target: { value: '123456789012' } });

        fireEvent.click(screen.getByText('Submit'));

        const twoFAform = getById(view.container, 'twoFAForm');
        expect(twoFAform).toBeInTheDocument();
        expect(view).not.toBeNull();
    })

    test('loading page with params - auto submit card', async () => {
        // Mock initialization that triggers 2FA immediately (responseCode "20")
        mockInitTransactionSession.mockResolvedValueOnce({
            responseCode: "20",
            responseMessage: "2FA Required",
            transactionID: "12345",
            orderNumber: "67890",
            cancelURL: "http://example.com/cancel",
            termURL: "http://example.com/termURL",
            MD: "abc",
            twoFaWebURL: "http://example.com/twoFaWebURL",
            paReq: "123456"
        });

        const getById = queryByAttribute.bind(null, 'id');
        const view = render(
            <MemoryRouter initialEntries={['?transactionId=12345&paReq=67890']}>
                <CheckoutPage />
            </MemoryRouter>
        );

        // Wait for the component to process the 2FA response
        await waitFor(() => {
            const twoFAform = getById(view.container, 'twoFAForm');
            expect(twoFAform).toBeInTheDocument();
        });

        const twoFAform = getById(view.container, 'twoFAForm');
        expect(twoFAform).toBeInTheDocument();
        expect(view).not.toBeNull();
    })

})
