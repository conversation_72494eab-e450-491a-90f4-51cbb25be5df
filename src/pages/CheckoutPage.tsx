import React, { useEffect, useState } from 'react';
import { submitPayment, initTransactionSession } from '../services/PaymentService';
import logo from '../assets/logo.png';
import { useLocation } from 'react-router-dom';
import { createMonths, createAvailableYears } from '../helper/Utils';
import LoadingOverlay from '../components/Loading';
import { InitTrasactionSessionRequest } from '../models/InitTransactionSession';

const CheckoutPage: React.FC = () => {
  const [twoFAformFields, setTwoFormFields] = useState({
    twoFaWebURL: '',
    termURL: '',
    MD: '',
    paReq: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const location = useLocation();
  const [transactionID, setTransactionID] = useState('');
  const [orderNumber, setOrderNumber] = useState('');

  const [months, setMonths] = useState<string[]>([]);
  const [years, setYears] = useState<number[]>([]);
  const [cardHolderName, setCardHolderName] = useState('');
  const [cardNumber, setCardNumber] = useState('');
  const [validMonth, setValidMonth] = useState('');
  const [validYear, setValidYear] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState('');
  const [cancelURL, setCancelURL] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [showElements, setShowElements] = useState(false);

   const initialValidationErrors = {
    cardHolderName: '',
    cardNumber: '',
    validUntil: ''
  };
  const [validationErrors, setValidationErrors] = useState(initialValidationErrors);

  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const txid = queryParams.get('transactionId');
    const pareq = queryParams.get('paReq');
    const initialize = async (transactionId:string, pareq: string) => {
      setIsLoading(true);

      try {
        const request: InitTrasactionSessionRequest = {
          transactionId: transactionId,
          pareq: pareq
        };
        console.log("call API");
        
        const response = await initTransactionSession(request);
        console.log('Init transaction session ', response)

        if (response.responseCode === "00") {
            setCancelURL(response.cancelURL);
            setTransactionID(response.transactionID);
            setOrderNumber(response.orderNumber);
            setMonths(createMonths);
            setYears(createAvailableYears);
            setShowElements(true);
        
        } else if (response.responseCode === "20") {
          const { twoFaWebURL, termURL, MD, paReq } = response;
          setTwoFormFields({ twoFaWebURL, termURL, MD, paReq });
              
        } else {
          throw Error(`init transaction failed - code: ${response.responseCode} - message: ${response.responseMessage}`);
        }
      } catch (error) {
        console.error('Error when init transaction: ', error);
        const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred while initializing the transaction.';
        setError(`An error occurred: ${errorMessage}`);      
      }
      setIsLoading(false);
    };

    if (txid) {
      setTransactionID(txid);
      initialize(txid, pareq || '');
    } else {
      setError('Invalid transaction parameters.');
    }

  }, []); 

  useEffect(() => {

    if (twoFAformFields.twoFaWebURL) {
      submit2FAForm();
    }
  }, [twoFAformFields.twoFaWebURL]);

  const handleCancel = () => {
    setIsLoading(true);
    const form = document.getElementById('cancelForm');
    if (form){
      const submitEvent = new Event('submit', { bubbles: true });
      form.dispatchEvent(submitEvent);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    // Validate form fields
    if (!validateCardHolderName() || !validateCardNumber() || !validateValidUntil()) {
      return;
    }
    // Prevent multiple submissions
    if (isSubmitting) {
      return;
    }

    setIsSubmitting(true);
    setValidationErrors(initialValidationErrors);
    setSubmitError('');
    setIsLoading(true);

    try {
      const response = await submitPayment(transactionID, cardHolderName, cardNumber, formatDateToString(new Date(parseInt(validYear), parseInt(validMonth)-1)));
      console.log('Submission successful:', response);
       // Extract the required values from the API response
      const { twoFaWebURL, termURL, MD, paReq, responseCode, responseMessage } = response;

      if (responseCode !== '00') {
        alert(responseMessage || 'Unable to submit your card data. Please contact system support!');
      } else {
        setTwoFormFields({ twoFaWebURL, termURL, MD, paReq });
        submit2FAForm();
      }
    } catch (error) {
      console.error('Error:', (error as Error).message);
      setSubmitError(`Error: ${(error as Error).message}`);
    }
    
    setIsLoading(false);
    setIsSubmitting(false);
  };

  const validateCardHolderName = (): boolean => {
    const nameRegex = /^[a-zA-Z0-9 ]+$/;
    if (cardHolderName.length < 3 || cardHolderName.length > 20 || !nameRegex.test(cardHolderName)) {
      setValidationErrors((prevState) => ({
        ...prevState,
        cardHolderName: 'Invalid card holder name. It should contain only letters, numbers, and spaces (3-20 characters).'
      }));
      return false;
    }
    return true;
  };

  const validateCardNumber = (): boolean => {
    if (cardNumber.length < 11 || cardNumber.length > 20 || !/^\d+$/.test(cardNumber)) {
      setValidationErrors((prevState) => ({
        ...prevState,
        cardNumber: 'Invalid card number. It should contain only numbers (11-20 digits).'
      }));
      return false;
    }
    return true;
  };
  const formatDateToString = (date: Date): string => {
    const year = date.getFullYear().toString();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');

    return `${year}${month}`;
  };
  const validateValidUntil = (): boolean => {
    const currentDate = new Date();
    const validUntilDate = new Date(parseInt(validYear), parseInt(validMonth));
    if (validUntilDate < currentDate) {
      setValidationErrors((prevState) => ({
        ...prevState,
        validUntil: 'Invalid expiry date. It should be equal to or greater than the current date.'
      }));
      return false;
    }
    return true;
  };
  
  const submit2FAForm = () => {
    var form = document.createElement("form");
    document.body.appendChild(form);
    form.id="generated2FAForm"
    form.method = "post";
    form.action = twoFAformFields.twoFaWebURL;
    form.submit();
  };
  if (error) {
    return (
    <div className="container d-flex flex-column align-items-center mt-4">
              <div className="card p-4" style={{ maxWidth: '500px', width: '100%' }}>
                <div className="d-flex justify-content-center">
                  {error}
                </div>
              </div>
      </div>
    )
  }

  return (
    <div className="container d-flex flex-column align-items-center mt-4">
      {isLoading && <LoadingOverlay />}
      {showElements && (
         <div className="card p-4" style={{ maxWidth: '500px', width: '100%' }}>

         <div className="d-flex justify-content-center">
             <img className="logo" src={logo} alt="Logo" style={{ maxWidth: '360px', width: '100%' }} />
         </div>
         <form onSubmit={handleSubmit} className="text-start">
           <div className="mb-3">
             <label htmlFor="cardHolderName" className="form-label pay-label">
               Card Holder Name
             </label>
             <input
               type="text"
               className="form-control"
               id="cardHolderName" aria-label="cardHolderName"
               value={cardHolderName}
               onChange={(e) => setCardHolderName(e.target.value)}
             />
             {validationErrors.cardHolderName && <div className="text-danger">{validationErrors.cardHolderName}</div>}
           </div>
           <div className="mb-3">
             <label htmlFor="cardNumber" className="form-label pay-label">
               Card Number
             </label>
             <input
               type="text"
               className="form-control"
               id="cardNumber" aria-label="cardNumber"
               value={cardNumber}
               onChange={(e) => setCardNumber(e.target.value)}
             />
             {validationErrors.cardNumber && <div className="text-danger">{validationErrors.cardNumber}</div>}
           </div>
           <div className="mb-3">
             <label className="form-label pay-label">Valid Until</label>
             <div className="d-flex">
               <select
                       className="form-select me-2"
                       value={validMonth}
                       onChange={(e) => setValidMonth(e.target.value)}>
                 <option value="">Month</option>
                 {months.map((month, index) => (
                   <option key={index} value={index + 1}>{month}</option>
                 ))}
               </select>
               <select
                 className="form-select"
                 value={validYear}
                 onChange={(e) => setValidYear(e.target.value)}>
                 <option value="">Year</option>
                 {years.map((year) => (
                   <option key={year} value={year}>{year}</option>
                 ))}
               </select>
             </div>
             {validationErrors.validUntil && <div className="text-danger">{validationErrors.validUntil}</div>}
           </div>
 
           {submitError && <div className="text-danger">{submitError}</div>}
           <div className="d-grid gap-2 mt-4">
             <button type="submit" className=" btn-pay" disabled={isSubmitting}>
               {isSubmitting ? 'Submitting...' : 'Submit'}
             </button>
             <button type="button" className=" mt-3 btn-pay" onClick={handleCancel}>
               Cancel
             </button>
           </div>
         </form>
       </div>
      )}
     
      <form id="twoFAForm" action={twoFAformFields.twoFaWebURL} method="POST">
      </form>
      <form id="cancelForm" action={cancelURL} method="POST">
        <input type="hidden" name="TransactionId" value={transactionID} />
        <input type="hidden" name="OrderNumber" value={orderNumber} />
        <input type="hidden" name="Description" value="The transaction has been canceled" />
      </form>
    </div>
  );
};

export default CheckoutPage;
