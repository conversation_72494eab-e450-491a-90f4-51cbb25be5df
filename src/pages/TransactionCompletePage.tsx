import { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { fetchTransactionDetails } from '../services/PaymentService';
import { TransactionDetails } from '../models/TransactionDetails';
import LoadingOverlay from '../components/Loading';
import { Alert } from 'react-bootstrap';

const TransactionCompletePage = () => {
  const [isLoading, setIsLoading] = useState(false);
  const location = useLocation();
  const [transactionID, setTransactionID] = useState('');
  const [transactionResult, setTransactionResult] = useState<TransactionDetails>({
    requestId:  '',
    transactionId:  '',
    responseMessage: ``,
    transactionDateTime: '',
    responseCode: '',
    cardToken: '',
    expiryDate: '',
    customData: '',
    signature: '',
    returnURL: ''
  });

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const transactionIDParam = searchParams.get('transactionId');
    const fetchTransactionResult = async (transactionID:string) => {
        setIsLoading(true);
        try {
          const response = await fetchTransactionDetails(transactionID);
          console.log(response)
          setTransactionResult(response);
        } catch (error) {
          setTransactionResult({
            ...transactionResult,
            responseMessage: `Error retrieving transaction details: ${error}`
          });
          console.error('Error retrieving transaction details:', error);
        }
        setIsLoading(false);
      };
    if (transactionIDParam) {
      setTransactionID(transactionIDParam);
      fetchTransactionResult(transactionIDParam);
    }
  }, []);

  useEffect(() => {
    if (transactionResult.returnURL) {
      var form = document.createElement("form");
      document.body.appendChild(form);
      form.method = "post";
      form.name="dcpSubmitForm"
      form.action = transactionResult.returnURL;
      
      var input1 = document.createElement("input");
      input1.type = "hidden";
      input1.name = "request_id";
      input1.value = transactionResult.requestId;
      form.appendChild(input1); // put it into the DOM

      form.submit();
    }
  }, [transactionResult.returnURL]);

  const renderTransactionStatus = () => {
    if (transactionResult) {
      if (transactionResult.responseCode !== '00' && transactionResult.responseCode !== '') {
        return (
          <div className="card p-4" style={{ maxWidth: '500px', width: '100%' }}>
            <div>

            <div style={{alignItems: 'center' }}>
              
                <Alert key='danger' variant='danger'>
                    Transaction Failed
                </Alert>
            </div>
            <div>Transaction ID: {transactionResult.transactionId}</div>
            <div>{transactionResult.responseMessage}</div>
            </div>
          </div>
          
        );
      }
    }
    return null;
  };
  
  return (
    <div className="container d-flex flex-column align-items-center mt-4">
        {isLoading && <LoadingOverlay />}
        {renderTransactionStatus()}

        {/* <form id="returnForm" action={transactionResult.returnURL} method="POST">
          <input type="hidden" name="transactionId" value={transactionResult.transactionId} />
          <input type="hidden" name="transactionDateTime" value={transactionResult.transactionDateTime} />
          <input type="hidden" name="responseCode" value={transactionResult.responseCode} />
          <input type="hidden" name="responseMessage" value={transactionResult.responseMessage} />
          <input type="hidden" name="cardToken" value={transactionResult.cardToken} />
          <input type="hidden" name="expiryDate" value={transactionResult.expiryDate} />
          <input type="hidden" name="additionalData" value={transactionResult.additionalData} />
          <input type="hidden" name="signature" value={transactionResult.signature} />
      </form> */}
    </div>

  );
};

export default TransactionCompletePage;
