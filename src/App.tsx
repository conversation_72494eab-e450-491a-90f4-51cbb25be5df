import React from 'react';
import './App.css';
import CheckoutPage from './pages/CheckoutPage';
import { Route, BrowserRouter, Routes } from 'react-router-dom';
import ErrorPage from './pages/Error';
import TransactionCompletePage from './pages/TransactionCompletePage';

function App() {
  return (
    <div className="App">
      <div className="App">
        
      <BrowserRouter>
      <Routes>
        <Route path="/checkout" element={<CheckoutPage />}>
        </Route>
        <Route path="/error" element={<ErrorPage />}>
        </Route>
        <Route path="/transaction/complete" element={<TransactionCompletePage />}>
        </Route>
      </Routes>
    </BrowserRouter>

      </div>
    </div>
  );
}

export default App;
