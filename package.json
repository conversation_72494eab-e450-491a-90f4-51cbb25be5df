{"name": "cabcharge-website", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.34", "@types/react": "^18.2.8", "@types/react-dom": "^18.2.4", "axios": "^1.4.0", "bootstrap": "^5.3.0", "env-cmd": "10.1.0", "react": "^18.2.0", "react-bootstrap": "^2.7.4", "react-dom": "^18.2.0", "react-router-dom": "^6.11.2", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"dev": "react-scripts start REACT_APP_ENV=dev", "start": "react-scripts start REACT_APP_ENV=dev", "build": "react-scripts build REACT_APP_ENV=sit", "build:sit": "env-cmd -f .env.sit react-scripts build", "build:uat1": "env-cmd -f .env.uat1 react-scripts build", "build:prod": "env-cmd -f .env.prod react-scripts build", "deploy": "react-scripts build REACT_APP_ENV=prod", "test": "react-scripts test --coverage --watchAll=false --coveragePathIgnorePatterns 'src/models' 'src/index.tsx' 'src/reportWebVitals.ts'", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"transform": {"^.+\\.[t|j]sx?$": "babel-jest"}, "transformIgnorePatterns": ["node_modules/(?!@shotgunjed)/", "src/theme/index\\.ts"]}}