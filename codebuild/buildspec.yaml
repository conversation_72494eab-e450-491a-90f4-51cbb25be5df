version: 0.2
phases:
  install:
    #If you use the Ubuntu standard image 2.0 or later, you must specify runtime-versions.
    #If you specify runtime-versions and use an image other than Ubuntu standard image 2.0, the build fails.
    runtime-versions:
      nodejs: 16
  pre_build:
    commands:
      - ls
      - echo ________ Installing sonarqube ________
      - mkdir -p /tmp/sonarqube
      - wget https://binaries.sonarsource.com/Distribution/sonar-scanner-cli/sonar-scanner-cli-4.8.0.2856-linux.zip -O /tmp/sonarqube/sonarqube.zip
      - unzip /tmp/sonarqube/sonarqube.zip -d /tmp/sonarqube
      - export PATH=$PATH:/tmp/sonarqube/sonar-scanner-4.8.0.2856-linux/bin/
      - echo ________ Installing dependencies ________
      - echo Installing source NPM dependencies...
      - npm install
      - npm run test
      - echo ________ Retrieving pull request info ________
      - env
      - echo ________ SonarCloud ________
      - >-
        sonar-scanner
        -Dsonar.login=$SONAR_TOKEN
        -Dsonar.host.url=$SONAR_HOST
        -Dsonar.projectKey=$SONAR_PROJECT
        -Dsonar.organization=$SONAR_ORGANIZATION
        -Dsonar.branch.name=$SONAR_BRANCH
        -Dsonar.scanner.force-deprecated-java-version=true
        -Dsonar.sources=./src
        -Dsonar.exclusions=**/*.test.tsx,src/reportWebVitals.ts,src/index.tsx
        -Dsonar.javascript.lcov.reportPaths=./coverage/lcov.info
  build:
    commands:
      - echo Build started on 'date'
      - npm run build:sit
  post_build:
    commands:
      - echo Build completed at 'date'
      - ls build/*
artifacts:
  files:
    - '**/*'
  base-directory: 'build'  
